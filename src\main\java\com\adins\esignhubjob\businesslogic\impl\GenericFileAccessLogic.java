package com.adins.esignhubjob.businesslogic.impl;

import java.io.File;
import java.io.IOException;

import org.apache.commons.io.FileUtils;
import org.springframework.stereotype.Component;

import com.adins.constants.Constants;
import com.adins.esignhubjob.businesslogic.BaseLogic;
import com.adins.esignhubjob.businesslogic.api.FileAccessLogic;
import com.adins.esignhubjob.model.table.TrDocumentD;
import com.aliyun.fc.runtime.Context;

@Component
public class GenericFileAccessLogic extends BaseLogic implements FileAccessLogic {
	String unsignedDir = System.getenv(Constants.ENV_VAR_EMETERAI_PAJAKKU_ON_PREM_STAMP_SOURCE);
	String signedDir = System.getenv(Constants.ENV_VAR_EMETERAI_PAJAKKU_ON_PREM_DEST);
	String stampDir = System.getenv(Constants.ENV_VAR_EMETERAI_PAJAKKU_ON_PREM_SPECIMEN);

	/**
	 * Writes a byte array to a file creating the file if it does not exist.
	 * If exists, file will be overwritten
	 */
	private void saveFile(byte[] fileContent, String directory, String filename, Context context) throws IOException {
		File dir = new File(directory);
		File file = new File(dir, filename);
		if (file.exists()) {
			context.getLogger().warn(String.format("File %1$s existed, file overridden", file.getAbsolutePath()));
		}
		FileUtils.writeByteArrayToFile(file, fileContent, false);
		context.getLogger().info(String.format("File %1$s saved", file.getAbsolutePath()));
	}
	
	/**
	 * Reads the contents of a file into a byte array
	 */
	private byte[] readFile(String directory, String filename, Context context) throws IOException {
		File dir = new File(directory);
		File file = new File(dir, filename);
		if (!file.exists()) {
			context.getLogger().info(String.format("File %1$s does not exist", file.getAbsolutePath()));
			return null;
		}
		context.getLogger().info(String.format("Reading %1$s", file.getAbsolutePath()));
		return FileUtils.readFileToByteArray(file);
	}
	
	/**
	 * @return {@code true} if the file or directory was deleted, otherwise {@code false}
	 */
	private boolean deleteFile(String directory, String filename, Context context) {
		File dir = new File(directory);
		File file = new File(dir, filename);
		context.getLogger().info(String.format("Deleting %1$s", file.getAbsolutePath()));
		return FileUtils.deleteQuietly(file);
	}

	@Override
	public void storeBaseStampDocument(byte[] fileContent, TrDocumentD document, Context context) throws IOException {
		String filename = document.getDocumentId() + ".pdf";
		context.getLogger().info(String.format("Kontrak %1$s, Dokumen %2$s, Upload document for stamping with filename %3$s", document.getTrDocumentH().getRefNumber(), document.getDocumentId(), filename));
		saveFile(fileContent, unsignedDir, filename, context);
	}

	@Override
	public byte[] getBaseStampDocument(TrDocumentD document, Context context) throws IOException {
		String filename = document.getDocumentId() + ".pdf";
		return readFile(unsignedDir, filename, context);
	}

	@Override
	public boolean deleteBaseStampDocument(TrDocumentD document, Context context) {
		String filename = document.getDocumentId() + ".pdf";
		return deleteFile(unsignedDir, filename, context);
	}

	@Override
	public void storeStampQr(byte[] imageContent, String serialNumber, Context context) throws IOException {
		String filename = serialNumber + ".png";
		context.getLogger().info(String.format("Storing meterai QR with filename %1$s", filename));
		saveFile(imageContent, stampDir, filename, context);
	}

	@Override
	public byte[] getStampQr(String serialNumber, Context context) throws IOException {
		String filename = serialNumber + ".png";
		return readFile(stampDir, filename, context);
	}

	@Override
	public boolean deleteStampQr(String serialNumber, Context context) {
		String filename = serialNumber + ".png";
		return deleteFile(stampDir, filename, context);
	}

	@Override
	public byte[] getStampedDocument(TrDocumentD document, Context context) throws IOException {
		String filename  = document.getDocumentId() + ".pdf";
		return readFile(signedDir, filename, context);
	}

	@Override
	public boolean deleteStampedDocument(TrDocumentD document, Context context) {
		String backupFilename = document.getDocumentId() + ".pdf.bckp";
		deleteFile(signedDir, backupFilename, context);
		
		String filename = document.getDocumentId() + ".pdf";
		return deleteFile(signedDir, filename, context);
	}

	@Override
	public boolean isStampedDocumentExists(TrDocumentD document, Context context) {
		String filename = document.getDocumentId() + ".pdf";
		File dir = new File(signedDir);	
		File file = new File(dir, filename);
		context.getLogger().info(document.getDocumentId() + " " + (file.exists() ? "exists" : "not exist"));
		return file.exists();
	}

	@Override
	public boolean deleteStampedDocumentbckp(TrDocumentD document, Context context) {
		String backupFilename = document.getDocumentId() + ".pdf.bckp";
		return deleteFile(signedDir, backupFilename, context);
	}
}
