package com.adins.esignhubjob.job;

import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.util.Date;
import java.util.List;

import org.apache.commons.lang3.StringUtils;

import com.adins.constants.Constants;
import com.adins.esignhubjob.BaseJobHandler;
import com.adins.esignhubjob.model.table.MsLov;
import com.adins.esignhubjob.model.table.TrDocumentD;
import com.adins.esignhubjob.model.table.TrSchedulerJob;
import com.aliyun.fc.runtime.Context;

public class EncryptTrDocumentDPersonalDataJob extends BaseJobHandler {

    private static final String AUDIT = "ENCRYPT_TRDOCUMENTD_JOB";
    private static final int DEFAULT_BATCH_SIZE = 100;

    @Override
    public void doHandleRequest(InputStream inputStream, OutputStream outputStream, Context context) throws IOException {
        
        context.getLogger().info("Starting TrDocumentD Personal Data Encryption Job");
        MsLov schedulerType = daoFactory.getLovDao().getLovByLovGroupAndCodeNewTran(
            Constants.LOV_GROUP_SCHEDULER_TYPE, Constants.CODE_LOV_SCHED_TYPE_DAILY);
        context.getLogger().info(String.format("Scheduler type %s fetched.", schedulerType.getCode()));
        
        MsLov jobType = daoFactory.getLovDao().getLovByLovGroupAndCodeNewTran(
            Constants.LOV_GROUP_JOB_TYPE, Constants.CODE_LOV_JOB_TYPE_ENCRYPT_TRDOCUMENTD_PERSONAL_DATA);
        context.getLogger().info(String.format("Job type %s fetched.", jobType.getCode()));
        
        Date startDate = new Date();
        Long totalDataProcessed = 0L;
        
        try {
            String batchSizeStr = System.getenv(Constants.ENV_VAR_DATA_PROCESSED_LIMIT);
            int batchSize = (batchSizeStr != null) ? Integer.parseInt(batchSizeStr) : DEFAULT_BATCH_SIZE;
            
            context.getLogger().info(String.format("Processing with batch size: %d", batchSize));
            
            List<TrDocumentD> documentsToProcess;
            do {
                documentsToProcess = daoFactory.getDocumentDao().getDocumentDsForPersonalDataEncryption(batchSize);
                if (!documentsToProcess.isEmpty()) {
                    context.getLogger().info(String.format("Processing batch of %d documents", documentsToProcess.size()));
                    
                    for (TrDocumentD document : documentsToProcess) {
                        try {
                            processDocumentEncryption(document, context);
                            totalDataProcessed++;
                        } catch (Exception e) {
                            context.getLogger().error(String.format("Error processing document ID %d: %s", 
                                document.getIdDocumentD(), e.getMessage()));
                        }
                    }
                    
                    context.getLogger().info(String.format("Completed batch. Total processed so far: %d", totalDataProcessed));
                }
                
            } while (!documentsToProcess.isEmpty() && documentsToProcess.size() == batchSize);
            
            Date endDate = new Date();
            
            context.getLogger().info(String.format("Job completed. Total documents processed: %d", totalDataProcessed));
            
            TrSchedulerJob schedulerJob = new TrSchedulerJob();
            schedulerJob.setSchedulerStart(startDate);
            schedulerJob.setSchedulerEnd(endDate);
            schedulerJob.setMsLovBySchedulerType(schedulerType);
            schedulerJob.setMsLovByJobType(jobType);
            schedulerJob.setDataProcessed(totalDataProcessed);
            schedulerJob.setUsrCrt(AUDIT);
            schedulerJob.setDtmCrt(new Date());
            schedulerJob.setNotes("Completed successfully");
            daoFactory.getSchedulerJobDao().insertSchedulerJobNewTrx(schedulerJob);
            
            context.getLogger().info("TrDocumentD Personal Data Encryption Job finished successfully");
            
        } catch (Exception e) {
            context.getLogger().error("Error in TrDocumentD Personal Data Encryption Job: " + e.getMessage());
            
            Date endDate = new Date();
            TrSchedulerJob schedulerJob = new TrSchedulerJob();
            schedulerJob.setSchedulerStart(startDate);
            schedulerJob.setSchedulerEnd(endDate);
            schedulerJob.setMsLovBySchedulerType(schedulerType);
            schedulerJob.setMsLovByJobType(jobType);
            schedulerJob.setDataProcessed(totalDataProcessed);
            schedulerJob.setUsrCrt(AUDIT);
            schedulerJob.setDtmCrt(new Date());
            schedulerJob.setNotes("Failed with error: " + StringUtils.left(e.getMessage(), 180));
            daoFactory.getSchedulerJobDao().insertSchedulerJobNewTrx(schedulerJob);
            
            throw new RuntimeException("TrDocumentD Personal Data Encryption Job failed", e);
        }
    }
    
    private void processDocumentEncryption(TrDocumentD document, Context context) {
        boolean updated = false;
        
        if (StringUtils.isNotBlank(document.getIdNo()) && document.getIdNoBytea() == null) {
            try {
                byte[] encryptedIdNo = logicFactory.getPersonalDataEncryptionLogic().encryptFromString(document.getIdNo());
                document.setIdNoBytea(encryptedIdNo);
                updated = true;
                context.getLogger().debug(String.format("Encrypted id_no for document ID %d", document.getIdDocumentD()));
            } catch (Exception e) {
                context.getLogger().error(String.format("Failed to encrypt id_no for document ID %d: %s", 
                    document.getIdDocumentD(), e.getMessage()));
                throw e;
            }
        }
        
        if (StringUtils.isNotBlank(document.getIdName()) && document.getIdNameBytea() == null) {
            try {
                byte[] encryptedIdName = logicFactory.getPersonalDataEncryptionLogic().encryptFromString(document.getIdName());
                document.setIdNameBytea(encryptedIdName);
                updated = true;
                context.getLogger().debug(String.format("Encrypted id_name for document ID %d", document.getIdDocumentD()));
            } catch (Exception e) {
                context.getLogger().error(String.format("Failed to encrypt id_name for document ID %d: %s", 
                    document.getIdDocumentD(), e.getMessage()));
                throw e;
            }
        }
        
        if (updated) {
            document.setUsrUpd(AUDIT);
            document.setDtmUpd(new Date());
            daoFactory.getDocumentDao().updateDocumentDNewTran(document);
            context.getLogger().debug(String.format("Updated document ID %d with encrypted data", document.getIdDocumentD()));
        } else {
            context.getLogger().debug(String.format("No encryption needed for document ID %d", document.getIdDocumentD()));
        }
    }
}
